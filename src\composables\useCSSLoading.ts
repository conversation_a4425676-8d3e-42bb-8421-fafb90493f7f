import { ref, nextTick } from 'vue'

interface LoadingOptions {
  text?: string
  lock?: boolean
  background?: string
}

interface LoadingInstance {
  close: () => void
}

/**
 * CSS Loading 组合式函数
 * 提供基于CSS动画的loading功能，不会被主线程阻塞
 */
export function useCSSLoading() {
  // 全局loading状态
  const isVisible = ref(false)
  const loadingText = ref('')
  const isLocked = ref(true)
  const backgroundColor = ref('rgba(255, 255, 255, 0.7)')

  /**
   * 显示loading
   * @param options 配置选项
   * @returns loading实例，包含close方法
   */
  function show(options: LoadingOptions = {}): LoadingInstance {
    // 设置配置
    loadingText.value = options.text || ''
    isLocked.value = options.lock !== false
    backgroundColor.value = options.background || 'rgba(255, 255, 255, 0.7)'
    
    // 显示loading
    isVisible.value = true

    // 返回实例
    return {
      close: () => {
        isVisible.value = false
      }
    }
  }

  /**
   * 隐藏loading
   */
  function hide() {
    isVisible.value = false
  }

  /**
   * 服务式调用（类似Element Plus的ElLoading.service）
   * @param options 配置选项
   * @returns loading实例
   */
  function service(options: LoadingOptions = {}): LoadingInstance {
    return show(options)
  }

  /**
   * 异步操作包装器
   * 自动显示和隐藏loading
   * @param asyncFn 异步函数
   * @param options loading配置
   * @returns 异步函数的结果
   */
  async function withLoading<T>(
    asyncFn: () => Promise<T>,
    options: LoadingOptions = {}
  ): Promise<T> {
    const loadingInstance = show(options)
    
    try {
      // 确保loading动画有机会渲染
      await nextTick()
      await new Promise(resolve => setTimeout(resolve, 50))
      
      // 执行异步操作
      const result = await asyncFn()
      return result
    } finally {
      // 无论成功还是失败都关闭loading
      loadingInstance.close()
    }
  }

  /**
   * Web Worker包装器
   * 使用Web Worker执行计算密集型任务，避免阻塞主线程
   * @param workerScript Worker脚本内容或URL
   * @param data 传递给Worker的数据
   * @param options loading配置
   * @returns Worker执行结果
   */
  async function withWorker<T>(
    workerScript: string | (() => void),
    data: any,
    options: LoadingOptions = {}
  ): Promise<T> {
    const loadingInstance = show(options)
    
    try {
      // 确保loading动画有机会渲染
      await nextTick()
      await new Promise(resolve => setTimeout(resolve, 50))
      
      return new Promise<T>((resolve, reject) => {
        let worker: Worker
        
        if (typeof workerScript === 'string') {
          // 如果是URL字符串
          worker = new Worker(workerScript)
        } else {
          // 如果是函数，创建内联Worker
          const blob = new Blob([`(${workerScript.toString()})()`], {
            type: 'application/javascript'
          })
          worker = new Worker(URL.createObjectURL(blob))
        }
        
        worker.onmessage = (event) => {
          resolve(event.data)
          worker.terminate()
        }
        
        worker.onerror = (error) => {
          reject(error)
          worker.terminate()
        }
        
        // 发送数据给Worker
        worker.postMessage(data)
      })
    } finally {
      loadingInstance.close()
    }
  }

  /**
   * 分片处理大型任务
   * 将大型任务分解为小块，在每块之间让出控制权
   * @param items 要处理的项目数组
   * @param processor 处理单个项目的函数
   * @param chunkSize 每次处理的项目数量
   * @param options loading配置
   * @returns 处理结果数组
   */
  async function withChunkedProcessing<T, R>(
    items: T[],
    processor: (item: T, index: number) => R | Promise<R>,
    chunkSize: number = 10,
    options: LoadingOptions = {}
  ): Promise<R[]> {
    const loadingInstance = show(options)
    const results: R[] = []
    
    try {
      // 确保loading动画有机会渲染
      await nextTick()
      await new Promise(resolve => setTimeout(resolve, 50))
      
      for (let i = 0; i < items.length; i += chunkSize) {
        const chunk = items.slice(i, i + chunkSize)
        
        // 处理当前块
        for (let j = 0; j < chunk.length; j++) {
          const result = await processor(chunk[j], i + j)
          results.push(result)
        }
        
        // 让出控制权，允许UI更新
        if (i + chunkSize < items.length) {
          await new Promise(resolve => setTimeout(resolve, 0))
        }
      }
      
      return results
    } finally {
      loadingInstance.close()
    }
  }

  return {
    // 状态
    isVisible,
    loadingText,
    isLocked,
    backgroundColor,
    
    // 基础方法
    show,
    hide,
    service,
    
    // 高级方法
    withLoading,
    withWorker,
    withChunkedProcessing
  }
}

// 创建全局实例
export const globalCSSLoading = useCSSLoading()
