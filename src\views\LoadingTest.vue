<template>
  <div class="loading-test">
    <h1>Loading 测试页面</h1>
    
    <div class="test-section">
      <h2>CSS Loading 测试</h2>
      <div class="button-group">
        <el-button @click="testCSSLoading" type="primary">
          测试CSS Loading
        </el-button>
        <el-button @click="testWithLoading" type="success">
          测试withLoading包装器
        </el-button>
        <el-button @click="testChunkedProcessing" type="warning">
          测试分片处理
        </el-button>
      </div>
    </div>

    <div class="test-section">
      <h2>MediaPipe Worker 测试</h2>
      <div class="button-group">
        <el-button @click="testWorkerDetection" type="primary" :disabled="!hasImages">
          测试Worker检测
        </el-button>
        <el-button @click="testMainThreadDetection" type="info" :disabled="!hasImages">
          测试主线程检测
        </el-button>
        <el-button @click="testSmartDetection" type="success" :disabled="!hasImages">
          测试智能检测
        </el-button>
      </div>
      <p v-if="!hasImages" class="warning">
        请先在导入图片页面上传微笑照和开口照
      </p>
    </div>

    <div class="test-section">
      <h2>性能对比</h2>
      <div class="performance-results" v-if="performanceResults.length > 0">
        <div v-for="result in performanceResults" :key="result.method" class="result-item">
          <strong>{{ result.method }}:</strong> 
          {{ result.duration }}ms 
          <span :class="result.success ? 'success' : 'error'">
            ({{ result.success ? '成功' : '失败' }})
          </span>
        </div>
      </div>
    </div>

    <div class="test-section">
      <h2>状态信息</h2>
      <div class="status-info">
        <p><strong>Worker状态:</strong> {{ workerStatus }}</p>
        <p><strong>检测器状态:</strong> {{ detectorStatus }}</p>
        <p><strong>是否有图片:</strong> {{ hasImages ? '是' : '否' }}</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { ElButton, ElMessage } from 'element-plus'
import { globalCSSLoading } from '@/composables/useCSSLoading'
import { useAIDetection } from '@/composables/useAIDetection'
import { useCommonStore } from '@/store/common'

// 响应式数据
const performanceResults = ref<Array<{
  method: string
  duration: number
  success: boolean
}>>([])

// 使用composables
const aiDetection = useAIDetection()
const commonStore = useCommonStore()

// 计算属性
const hasImages = computed(() => {
  return !!(commonStore.smileImage && commonStore.mouthImage)
})

const workerStatus = computed(() => {
  return aiDetection.isWorkerReady.value ? '已就绪' : '未初始化'
})

const detectorStatus = computed(() => {
  return aiDetection.isDetectorReady() ? '已就绪' : '未初始化'
})

// 测试方法
async function testCSSLoading() {
  const loadingInstance = globalCSSLoading.service({
    text: '测试CSS Loading...',
    lock: true
  })

  // 模拟长时间操作
  await new Promise(resolve => setTimeout(resolve, 3000))
  
  loadingInstance.close()
  ElMessage.success('CSS Loading测试完成')
}

async function testWithLoading() {
  try {
    await globalCSSLoading.withLoading(
      async () => {
        // 模拟CPU密集型任务
        const start = Date.now()
        let count = 0
        while (Date.now() - start < 2000) {
          count++
        }
        return count
      },
      {
        text: '执行CPU密集型任务...',
        lock: true
      }
    )
    ElMessage.success('withLoading测试完成')
  } catch (error) {
    ElMessage.error('withLoading测试失败')
  }
}

async function testChunkedProcessing() {
  try {
    const items = Array.from({ length: 1000 }, (_, i) => i)
    
    await globalCSSLoading.withChunkedProcessing(
      items,
      (item) => {
        // 模拟处理每个项目
        return item * 2
      },
      50, // 每次处理50个项目
      {
        text: '分片处理1000个项目...',
        lock: true
      }
    )
    
    ElMessage.success('分片处理测试完成')
  } catch (error) {
    ElMessage.error('分片处理测试失败')
  }
}

async function testWorkerDetection() {
  if (!hasImages.value) return

  const startTime = performance.now()
  try {
    const success = await aiDetection.detectFromStoreImagesWithWorker()
    const duration = performance.now() - startTime
    
    performanceResults.value.push({
      method: 'Worker检测',
      duration: Math.round(duration),
      success
    })
    
    ElMessage.success(`Worker检测${success ? '成功' : '失败'}`)
  } catch (error) {
    const duration = performance.now() - startTime
    performanceResults.value.push({
      method: 'Worker检测',
      duration: Math.round(duration),
      success: false
    })
    ElMessage.error('Worker检测失败')
  }
}

async function testMainThreadDetection() {
  if (!hasImages.value) return

  const startTime = performance.now()
  try {
    const success = await aiDetection.detectFromStoreImages()
    const duration = performance.now() - startTime
    
    performanceResults.value.push({
      method: '主线程检测',
      duration: Math.round(duration),
      success
    })
    
    ElMessage.success(`主线程检测${success ? '成功' : '失败'}`)
  } catch (error) {
    const duration = performance.now() - startTime
    performanceResults.value.push({
      method: '主线程检测',
      duration: Math.round(duration),
      success: false
    })
    ElMessage.error('主线程检测失败')
  }
}

async function testSmartDetection() {
  if (!hasImages.value) return

  const startTime = performance.now()
  try {
    const success = await globalCSSLoading.withLoading(
      () => aiDetection.smartDetection(),
      {
        text: '智能检测中...',
        lock: true
      }
    )
    
    const duration = performance.now() - startTime
    
    performanceResults.value.push({
      method: '智能检测',
      duration: Math.round(duration),
      success
    })
    
    ElMessage.success(`智能检测${success ? '成功' : '失败'}`)
  } catch (error) {
    const duration = performance.now() - startTime
    performanceResults.value.push({
      method: '智能检测',
      duration: Math.round(duration),
      success: false
    })
    ElMessage.error('智能检测失败')
  }
}

// 生命周期
onMounted(() => {
  // 初始化Worker
  aiDetection.initializeWorker()
})
</script>

<style scoped>
.loading-test {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.test-section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
}

.button-group {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.performance-results {
  margin-top: 15px;
}

.result-item {
  padding: 8px;
  margin: 5px 0;
  background: #f5f7fa;
  border-radius: 4px;
}

.success {
  color: #67c23a;
}

.error {
  color: #f56c6c;
}

.warning {
  color: #e6a23c;
  font-style: italic;
}

.status-info p {
  margin: 8px 0;
}
</style>
