import { ref } from 'vue'

/**
 * 使用Web Worker的MediaPipe检测
 * 避免阻塞主线程，提供流畅的用户体验
 */
export function useMediaPipeWorker() {
  const isWorkerReady = ref(false)
  const isDetecting = ref(false)
  let worker: Worker | null = null

  /**
   * 初始化Worker
   */
  async function initializeWorker(): Promise<boolean> {
    if (worker && isWorkerReady.value) return true

    try {
      // 创建Worker
      worker = new Worker(new URL('../workers/mediapipeWorker.ts', import.meta.url), {
        type: 'module'
      })

      // 监听Worker消息
      return new Promise<boolean>((resolve) => {
        worker!.onmessage = (event) => {
          const { type, success, error } = event.data

          if (type === 'init') {
            isWorkerReady.value = success
            resolve(success)
          } else if (type === 'error') {
            console.error('Worker初始化错误:', error)
            resolve(false)
          }
        }

        worker!.onerror = (error) => {
          console.error('Worker错误:', error)
          resolve(false)
        }

        // 发送初始化消息
        worker!.postMessage({ type: 'init' })
      })
    } catch (error) {
      console.error('Worker创建失败:', error)
      return false
    }
  }

  /**
   * 从图片检测面部关键点
   * @param imageElement HTML图片元素
   * @returns 检测结果
   */
  async function detectFromImage(imageElement: HTMLImageElement): Promise<any> {
    if (!worker || !isWorkerReady.value) {
      const initialized = await initializeWorker()
      if (!initialized) {
        throw new Error('Worker初始化失败')
      }
    }

    isDetecting.value = true

    try {
      // 将图片转换为ImageData
      const canvas = document.createElement('canvas')
      const ctx = canvas.getContext('2d')!
      
      canvas.width = imageElement.naturalWidth
      canvas.height = imageElement.naturalHeight
      ctx.drawImage(imageElement, 0, 0)
      
      const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height)

      // 发送检测请求到Worker
      return new Promise((resolve, reject) => {
        const handleMessage = (event: MessageEvent) => {
          const { type, result, error } = event.data

          if (type === 'detect') {
            worker!.removeEventListener('message', handleMessage)
            resolve(result)
          } else if (type === 'error') {
            worker!.removeEventListener('message', handleMessage)
            reject(new Error(error))
          }
        }

        worker!.addEventListener('message', handleMessage)
        worker!.postMessage({
          type: 'detect',
          data: { imageData }
        })
      })
    } finally {
      isDetecting.value = false
    }
  }

  /**
   * 检测单张图片（从base64字符串）
   * @param imageSrc 图片源
   * @param label 图片标签
   * @returns 检测结果
   */
  async function detectSingleImage(imageSrc: string, label: string) {
    try {
      console.log(`开始检测${label}...`)

      // 创建图片元素
      const img = new Image()
      img.crossOrigin = 'anonymous'

      // 等待图片加载
      await new Promise<void>((resolve, reject) => {
        img.onload = () => resolve()
        img.onerror = () => reject(new Error(`${label}加载失败`))
        img.src = imageSrc
      })

      // 进行检测
      const result = await detectFromImage(img)

      if (result) {
        console.log(`${label}检测成功`)
        return result
      } else {
        console.warn(`${label}未检测到面部`)
        return null
      }
    } catch (error) {
      console.error(`${label}检测失败:`, error)
      return null
    }
  }

  /**
   * 检测两张照片（微笑照和开口照）
   * @param smileImage 微笑照片
   * @param mouthImage 开口照片
   * @returns 合并的检测结果
   */
  async function detectBothImages(smileImage: string, mouthImage: string) {
    try {
      console.log('开始检测两张照片...')

      // 检测微笑照片
      const smileResult = await detectSingleImage(smileImage, '微笑照')
      if (!smileResult) {
        throw new Error('微笑照检测失败')
      }

      // 检测开口照片
      const mouthResult = await detectSingleImage(mouthImage, '开口照')
      if (!mouthResult) {
        throw new Error('开口照检测失败')
      }

      // 合并检测结果
      const mergedResult = {
        points: smileResult.points, // 面部关键点使用微笑照的
        smileLipPoints: smileResult.smileLipPoints, // 微笑照唇线点
        mouthLipPoints: mouthResult.mouthLipPoints, // 开口照唇线点
        smileAlignPoints: smileResult.smileAlignPoints, // 微笑照对齐点
        mouthAlignPoints: mouthResult.mouthAlignPoints, // 开口照对齐点
        confidence: Math.min(smileResult.confidence, mouthResult.confidence)
      }

      console.log('两张照片检测完成')
      return mergedResult
    } catch (error) {
      console.error('检测两张照片失败:', error)
      throw error
    }
  }

  /**
   * 销毁Worker
   */
  function dispose() {
    if (worker) {
      worker.terminate()
      worker = null
      isWorkerReady.value = false
      console.log('MediaPipe Worker已销毁')
    }
  }

  return {
    // 状态
    isWorkerReady,
    isDetecting,

    // 方法
    initializeWorker,
    detectFromImage,
    detectSingleImage,
    detectBothImages,
    dispose
  }
}
