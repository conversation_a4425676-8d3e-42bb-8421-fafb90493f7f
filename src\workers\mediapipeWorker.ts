// MediaPipe Web Worker
// 在独立线程中执行MediaPipe检测，避免阻塞主线程

import * as faceLandmarksDetection from '@tensorflow-models/face-landmarks-detection'
import '@tensorflow/tfjs-backend-webgl'

let detector: faceLandmarksDetection.FaceLandmarksDetector | null = null

// MediaPipe关键点索引映射
const MEDIAPIPE_KEYPOINT_MAPPING = {
  leftEye: 468,
  rightEye: 473,
  leftNostril: 48,
  rightNostril: 278,
  leftMouth: 61,
  rightMouth: 291
}

// 内唇轮廓关键点索引
const LIP_KEYPOINTS = [
  78, 95, 88, 178, 87, 14, 317, 402, 318, 324, 308,
  415, 310, 311, 312, 13, 82, 81, 80
]

/**
 * 初始化MediaPipe检测器
 */
async function initializeDetector(): Promise<boolean> {
  if (detector) return true

  try {
    console.log('Worker: 正在初始化MediaPipe检测器...')
    
    const model = faceLandmarksDetection.SupportedModels.MediaPipeFaceMesh
    const detectorConfig = {
      runtime: 'mediapipe' as const,
      solutionPath: 'https://cdn.jsdelivr.net/npm/@mediapipe/face_mesh',
      refineLandmarks: true,
      maxFaces: 1
    }

    detector = await faceLandmarksDetection.createDetector(model, detectorConfig)
    console.log('Worker: MediaPipe检测器初始化成功')
    return true
  } catch (error) {
    console.error('Worker: MediaPipe检测器初始化失败:', error)
    return false
  }
}

/**
 * 检测面部关键点
 */
async function detectFace(imageData: ImageData): Promise<any> {
  try {
    if (!detector) {
      const initialized = await initializeDetector()
      if (!initialized) {
        throw new Error('检测器初始化失败')
      }
    }

    console.log('Worker: 开始检测面部关键点...')
    const startTime = performance.now()

    // 从ImageData创建Canvas
    const canvas = new OffscreenCanvas(imageData.width, imageData.height)
    const ctx = canvas.getContext('2d')!
    ctx.putImageData(imageData, 0, 0)

    // 执行检测
    const faces = await detector!.estimateFaces(canvas as any, {
      flipHorizontal: false
    })

    const endTime = performance.now()
    console.log(`Worker: 面部检测完成，耗时: ${(endTime - startTime).toFixed(2)}ms`)

    if (faces.length === 0) {
      return null
    }

    // 处理检测结果
    const face = faces[0]
    const keypoints = face.keypoints

    // 提取所需数据
    const facePoints = extractRequiredFacePoints(keypoints)
    const lipPoints = extractLipPoints(keypoints)
    const alignPoints = extractAlignPoints(keypoints)

    return {
      points: facePoints,
      smileLipPoints: lipPoints,
      mouthLipPoints: lipPoints,
      smileAlignPoints: alignPoints,
      mouthAlignPoints: alignPoints,
      confidence: face.score || 0.9,
      totalKeypoints: keypoints.length
    }
  } catch (error) {
    console.error('Worker: 面部检测失败:', error)
    throw error
  }
}

/**
 * 提取项目所需的6个关键点
 */
function extractRequiredFacePoints(keypoints: any[]) {
  const facePoints: any[] = []

  Object.entries(MEDIAPIPE_KEYPOINT_MAPPING).forEach(([name, index]) => {
    if (index < keypoints.length) {
      const point = keypoints[index]
      facePoints.push({
        name,
        x: point.x,
        y: point.y
      })
    }
  })

  return facePoints
}

/**
 * 提取唇线点
 */
function extractLipPoints(keypoints: any[]) {
  const lipPoints: any[] = []

  LIP_KEYPOINTS.forEach((index) => {
    if (index < keypoints.length) {
      const point = keypoints[index]
      lipPoints.push({
        x: point.x,
        y: point.y
      })
    }
  })

  return lipPoints
}

/**
 * 提取对齐点
 */
function extractAlignPoints(keypoints: any[]) {
  const alignPoints: any[] = []

  const upperLipLeft = 82
  const upperLipRight = 312
  const lowerLipLeft = 87
  const lowerLipRight = 317

  if (
    upperLipLeft < keypoints.length &&
    upperLipRight < keypoints.length &&
    lowerLipLeft < keypoints.length &&
    lowerLipRight < keypoints.length
  ) {
    const upperLeft = keypoints[upperLipLeft]
    const upperRight = keypoints[upperLipRight]
    const lowerLeft = keypoints[lowerLipLeft]
    const lowerRight = keypoints[lowerLipRight]

    // 计算对齐点
    const leftAlignPoint = {
      x: (upperLeft.x + lowerLeft.x) / 2,
      y: (upperLeft.y + lowerLeft.y) / 2
    }

    const rightAlignPoint = {
      x: (upperRight.x + lowerRight.x) / 2,
      y: (upperRight.y + lowerRight.y) / 2
    }

    alignPoints.push(leftAlignPoint, rightAlignPoint)
  } else {
    // 备用方案
    const leftMouthCorner = 61
    const rightMouthCorner = 291

    const backupIndices = [leftMouthCorner, rightMouthCorner]
    backupIndices.forEach((index) => {
      if (index < keypoints.length) {
        const point = keypoints[index]
        alignPoints.push({
          x: point.x,
          y: point.y
        })
      }
    })
  }

  return alignPoints
}

// Worker消息处理
self.onmessage = async (event) => {
  const { type, data } = event.data

  try {
    switch (type) {
      case 'init':
        const success = await initializeDetector()
        self.postMessage({ type: 'init', success })
        break

      case 'detect':
        const result = await detectFace(data.imageData)
        self.postMessage({ type: 'detect', result })
        break

      default:
        throw new Error(`未知的消息类型: ${type}`)
    }
  } catch (error) {
    self.postMessage({ 
      type: 'error', 
      error: error instanceof Error ? error.message : String(error) 
    })
  }
}
