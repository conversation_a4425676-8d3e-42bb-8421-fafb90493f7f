import { useFaceData } from './useFaceData'
import { useMediaPipeFaceDetection } from './useMediaPipeFaceDetection'
import { useMediaPipeWorker } from './useMediaPipeWorker'
import { useCommonStore } from '@/store/common'

/**
 * AI检测数据相关功能的组合式函数
 * 专注于从AI获取数据，然后通过useFaceData设置数据
 * 支持MediaPipe和API两种检测方式
 */
export function useAIDetection() {
  // 使用面部数据管理
  const faceData = useFaceData()

  // 使用MediaPipe检测
  const mediaPipeDetection = useMediaPipeFaceDetection()

  // 使用MediaPipe Worker检测（非阻塞）
  const mediaPipeWorker = useMediaPipeWorker()

  // 使用store获取图片数据
  const commonStore = useCommonStore()

  /**
   * 通过接口获取AI检测数据（面部关键点、唇线点、对齐点等）
   * @param width 画布宽度（可选）
   * @param height 画布高度（可选）
   */
  async function fetchAIDetectionData(width?: number, height?: number) {
    try {
      // 构建API请求URL
      let url = import.meta.env.VITE_API_BASE_URL + '/face/landmark'
      if (width && height) {
        url += `?width=${width}&height=${height}`
      }

      // 发送请求
      const res = await fetch(url)
      if (!res.ok) throw new Error('接口请求失败')

      // 解析响应
      const data = await res.json()

      // 处理响应数据
      if (data && data.data && Array.isArray(data.data.points)) {
        // 使用faceData设置数据
        faceData.setAllFaceData(
          data.data.points,
          data.data.smileLipPoints,
          data.data.mouthLipPoints,
          data.data.smileAlignPoints,
          data.data.mouthAlignPoints
        )
      } else {
        // 响应格式不正确，重置数据
        faceData.resetFaceData()
        console.warn('AI检测接口返回数据格式不正确', data)
      }
    } catch (err) {
      // 请求出错，重置数据
      faceData.resetFaceData()
      console.error('AI检测接口请求或处理出错', err)
    }
  }

  /**
   * 从图片进行AI检测（使用MediaPipe）
   * @param imageElement HTML图片元素
   * @returns 检测是否成功
   */
  async function detectFromImage(imageElement: HTMLImageElement): Promise<boolean> {
    try {
      console.log('开始MediaPipe面部检测...')
      const result = await mediaPipeDetection.detectFaceFromImage(imageElement)

      if (result) {
        // 设置检测到的数据
        faceData.setAllFaceData(
          result.points,
          result.smileLipPoints,
          result.mouthLipPoints,
          result.smileAlignPoints,
          result.mouthAlignPoints
        )

        console.log('MediaPipe检测成功:', {
          facePoints: result.points.length,
          lipPoints: result.smileLipPoints.length,
          confidence: result.confidence
        })

        return true
      } else {
        console.warn('MediaPipe未检测到面部')
        faceData.resetFaceData()
        return false
      }
    } catch (error) {
      console.error('MediaPipe检测失败:', error)
      faceData.resetFaceData()
      throw error
    }
  }

  /**
   * 从store中的图片进行检测
   * @returns 检测是否成功
   */
  async function detectFromStoreImages(): Promise<boolean> {
    try {
      if (!commonStore.smileImage) {
        throw new Error('未找到微笑照片')
      }

      if (!commonStore.mouthImage) {
        throw new Error('未找到开口照片')
      }

      console.log('开始检测两张照片...')

      // 检测微笑照片
      const smileResult = await detectSingleImage(commonStore.smileImage, '微笑照')
      if (!smileResult) {
        throw new Error('微笑照检测失败')
      }

      // 在两次检测之间添加短暂延迟，让UI有机会更新
      await new Promise((resolve) => setTimeout(resolve, 50))

      // 检测开口照片
      const mouthResult = await detectSingleImage(commonStore.mouthImage, '开口照')
      if (!mouthResult) {
        throw new Error('开口照检测失败')
      }

      // 合并两张照片的检测结果
      faceData.setAllFaceData(
        smileResult.points, // 面部关键点使用微笑照的
        smileResult.smileLipPoints, // 微笑照唇线点
        mouthResult.mouthLipPoints, // 开口照唇线点
        smileResult.smileAlignPoints, // 微笑照对齐点
        mouthResult.mouthAlignPoints // 开口照对齐点
      )

      console.log('两张照片检测完成')
      return true
    } catch (error) {
      console.error('从store图片检测失败:', error)
      throw error
    }
  }

  /**
   * 使用Web Worker从store中的图片进行检测（非阻塞）
   * @returns 检测是否成功
   */
  async function detectFromStoreImagesWithWorker(): Promise<boolean> {
    try {
      if (!commonStore.smileImage) {
        throw new Error('未找到微笑照片')
      }

      if (!commonStore.mouthImage) {
        throw new Error('未找到开口照片')
      }

      console.log('开始使用Worker检测两张照片...')

      // 使用Worker检测两张照片
      const result = await mediaPipeWorker.detectBothImages(
        commonStore.smileImage,
        commonStore.mouthImage
      )

      if (result) {
        // 设置检测到的数据
        faceData.setAllFaceData(
          result.points,
          result.smileLipPoints,
          result.mouthLipPoints,
          result.smileAlignPoints,
          result.mouthAlignPoints
        )

        console.log('Worker检测完成')
        return true
      } else {
        console.warn('Worker检测失败')
        return false
      }
    } catch (error) {
      console.error('Worker检测失败:', error)
      throw error
    }
  }

  /**
   * 检测单张图片
   * @param imageSrc 图片源
   * @param label 图片标签（用于日志）
   * @returns 检测结果
   */
  async function detectSingleImage(imageSrc: string, label: string) {
    try {
      console.log(`开始检测${label}...`)

      // 创建图片元素
      const img = new Image()
      img.crossOrigin = 'anonymous'

      // 等待图片加载
      await new Promise<void>((resolve, reject) => {
        img.onload = () => resolve()
        img.onerror = () => reject(new Error(`${label}加载失败`))
        img.src = imageSrc
      })

      // 进行检测
      const result = await mediaPipeDetection.detectFaceFromImage(img)

      if (result) {
        console.log(`${label}检测成功`)
        return result
      } else {
        console.warn(`${label}未检测到面部`)
        return null
      }
    } catch (error) {
      console.error(`${label}检测失败:`, error)
      return null
    }
  }

  /**
   * 智能检测：优先使用Worker，然后MediaPipe，最后回退到API
   * @param width 画布宽度（可选）
   * @param height 画布高度（可选）
   * @param useWorker 是否使用Web Worker（默认true）
   * @returns 检测是否成功
   */
  async function smartDetection(
    width?: number,
    height?: number,
    useWorker: boolean = true
  ): Promise<boolean> {
    try {
      if (useWorker) {
        // 首先尝试Worker检测（非阻塞）
        console.log('尝试Worker检测...')
        try {
          const workerSuccess = await detectFromStoreImagesWithWorker()
          if (workerSuccess) {
            console.log('Worker检测成功')
            return true
          }
        } catch (workerError) {
          console.warn('Worker检测失败，回退到主线程检测:', workerError)
        }
      }

      // Worker失败或不使用Worker，尝试主线程MediaPipe检测
      console.log('尝试主线程MediaPipe检测...')
      const mediaPipeSuccess = await detectFromStoreImages()

      if (mediaPipeSuccess) {
        console.log('主线程MediaPipe检测成功')
        return true
      }

      // MediaPipe失败，回退到API检测
      console.log('MediaPipe检测失败，回退到API检测...')
      await fetchAIDetectionData(width, height)

      // 检查API检测是否成功（通过检查是否有面部关键点）
      if (faceData.smileFaceLandmarks.value.length > 0) {
        console.log('API检测成功')
        return true
      } else {
        console.log('所有检测方法都失败了')
        return false
      }
    } catch (error) {
      console.error('智能检测失败:', error)
      return false
    }
  }

  // 返回faceData的所有属性和方法，以及AI检测特有的方法
  return {
    ...faceData,

    // 原有方法
    fetchAIDetectionData,

    // MediaPipe方法
    detectFromImage,
    detectFromStoreImages,
    detectSingleImage,
    smartDetection,

    // Worker方法
    detectFromStoreImagesWithWorker,

    // MediaPipe工具方法
    initializeDetector: mediaPipeDetection.initializeDetector,
    isDetectorReady: mediaPipeDetection.isDetectorReady,
    dispose: mediaPipeDetection.dispose,

    // Worker工具方法
    initializeWorker: mediaPipeWorker.initializeWorker,
    isWorkerReady: mediaPipeWorker.isWorkerReady,
    disposeWorker: mediaPipeWorker.dispose
  }
}
